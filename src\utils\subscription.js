const axios = require('axios');
const https = require('https');
const yaml = require('js-yaml');

/**
 * 从订阅链接获取代理节点
 * @param {string} url 订阅链接
 * @returns {Promise<Array>} 代理节点列表
 */
async function fetchProxiesFromSubscription(url) {
    console.log(`正在获取订阅内容: ${url}`);

    // 设置请求配置
    const axiosConfig = {
        responseType: 'text',
        headers: { 'User-Agent': 'ClashForWindows/0.19.0' },
        timeout: 15000 // 15秒超时
    };

    // 特定订阅源特殊处理
    if (url.includes('su.xfjhchr.com') || url.includes('token=657546a027866da3278413ae781daba9')) {
        axiosConfig.httpsAgent = new https.Agent({ rejectUnauthorized: false }); // 禁用SSL证书验证
        axiosConfig.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    }

    try {
        const response = await axios.get(url, axiosConfig);
        let content = response.data.toString().trim();

        // 检查是否为HTML
        if (content.startsWith('<!DOCTYPE') || content.startsWith('<html')) {
            // 尝试从HTML中提取Base64内容
            const base64Match = content.match(/[A-Za-z0-9+/=]{50,}/);
            if (base64Match && base64Match[0]) {
                console.log('从HTML中提取到疑似Base64内容');
                content = base64Match[0];
            } else {
                throw new Error('订阅返回了HTML内容，可能需要登录或格式错误');
            }
        }

        let proxiesList = [];

        // 尝试解码Base64内容
        if (/^[A-Za-z0-9+/=\s\r\n]+$/g.test(content) && content.length > 20) {
            try {
                // 移除所有空白字符再解码
                const cleanedBase64 = content.replace(/\s/g, '');
                const decodedContent = Buffer.from(cleanedBase64, 'base64').toString('utf-8');

                // 检查解码后内容是否为代理链接
                if (decodedContent.includes('ss://') || decodedContent.includes('vmess://') || decodedContent.includes('trojan://')) {
                    // 解析为链接格式
                    proxiesList = parseProxyLinks(decodedContent);
                } else {
                    // 尝试解析为Clash YAML格式
                    try {
                        const parsedYaml = yaml.load(decodedContent);
                        if (parsedYaml && parsedYaml.proxies && Array.isArray(parsedYaml.proxies)) {
                            proxiesList = parsedYaml.proxies;
                            console.log('Base64解码后识别为Clash proxies列表');
                        }
                    } catch (yamlError) {
                        console.log('Base64解码后内容非YAML格式，尝试直接解析');
                    }
                }
            } catch (e) {
                console.log('Base64解码失败，尝试直接解析原始内容');
            }
        }

        // 如果上述解析都失败，尝试直接解析原始内容
        if (proxiesList.length === 0) {
            // 尝试解析为Clash YAML格式
            try {
                const parsedYaml = yaml.load(content);
                if (parsedYaml && parsedYaml.proxies && Array.isArray(parsedYaml.proxies)) {
                    proxiesList = parsedYaml.proxies;
                    console.log('订阅内容识别为Clash proxies列表');
                } else {
                    // 尝试解析为代理链接
                    proxiesList = parseProxyLinks(content);
                }
            } catch (yamlError) {
                // 尝试解析为代理链接
                proxiesList = parseProxyLinks(content);
            }
        }

        // 为每个节点添加区域信息和Emoji
        proxiesList.forEach(proxy => {
            const regionInfo = detectNodeRegion(proxy, url);
            proxy.name = regionInfo.newName; // 更新节点名称
        });

        console.log(`从订阅中解析到 ${proxiesList.length} 个代理节点`);
        return proxiesList;
    } catch (error) {
        console.error('获取订阅失败:', error.message);
        throw new Error(`获取订阅失败: ${error.message}`);
    }
}

/**
 * 解析代理链接（如ss://、vmess://等）
 * @param {string} content 包含代理链接的内容
 * @returns {Array} 解析后的代理列表
 */
function parseProxyLinks(content) {
    const lines = content.split('\n');
    const proxies = [];

    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        try {
            // 解析SS链接
            if (trimmedLine.startsWith('ss://')) {
                let ssLink = trimmedLine.split('#')[0].replace('ss://', '');
                let name = 'SS节点';
                if (trimmedLine.includes('#')) {
                    try { name = decodeURIComponent(trimmedLine.split('#')[1]); } catch (e) { /* 忽略解码错误 */ }
                }

                let decodedData;
                // 尝试Base64解码 (常见的ss://BASE64#name 格式)
                if (!ssLink.includes('@')) { // 通常Base64编码的没有@符号在外面
                    try { decodedData = Buffer.from(ssLink, 'base64').toString(); } catch (e) { /* 忽略解码错误 */ }
                } else { // method:pass@server:port 格式
                    decodedData = ssLink;
                }

                if (decodedData && decodedData.includes(':') && decodedData.includes('@')) {
                    const [methodPass, serverPort] = decodedData.split('@');
                    const [method, password] = methodPass.split(':');
                    const [server, portStr] = serverPort.split(':');
                    const port = parseInt(portStr);

                    if (server && port && method && password) {
                        proxies.push({ name, type: 'ss', server, port, cipher: method, password });
                    }
                }
            }
            // 解析VMess链接
            else if (trimmedLine.startsWith('vmess://')) {
                try {
                    const vmessData = Buffer.from(trimmedLine.replace('vmess://', ''), 'base64').toString();
                    const vmessConfig = JSON.parse(vmessData);
                    if (vmessConfig.add && vmessConfig.port && vmessConfig.id) {
                        proxies.push({
                            name: vmessConfig.ps || 'VMess节点',
                            type: 'vmess',
                            server: vmessConfig.add,
                            port: parseInt(vmessConfig.port),
                            uuid: vmessConfig.id,
                            alterId: parseInt(vmessConfig.aid || 0),
                            cipher: vmessConfig.scy || 'auto',
                            tls: vmessConfig.tls === 'tls',
                            network: vmessConfig.net || 'tcp',
                            'ws-path': vmessConfig.path || (vmessConfig.net === 'ws' ? '/' : undefined),
                            'ws-headers': vmessConfig.host ? { Host: vmessConfig.host } : undefined
                        });
                    }
                } catch (e) { console.error('解析VMess链接失败:', e); }
            }
            // 解析Trojan链接
            else if (trimmedLine.startsWith('trojan://')) {
                try {
                    // trojan://password@server:port?params#name
                    const trojanUrl = new URL(trimmedLine);
                    const password = trojanUrl.username || trojanUrl.pathname.split('@')[0].replace('//', '');
                    const server = trojanUrl.hostname;
                    const port = parseInt(trojanUrl.port);
                    const name = trojanUrl.hash ? decodeURIComponent(trojanUrl.hash.substring(1)) : 'Trojan节点';

                    if (server && port && password) {
                        const trojanProxy = {
                            name,
                            type: 'trojan',
                            server,
                            port,
                            password,
                            tls: true, // trojan默认使用TLS
                            'skip-cert-verify': false
                        };

                        // 解析查询参数
                        const params = new URLSearchParams(trojanUrl.search);
                        if (params.has('sni')) trojanProxy.sni = params.get('sni');
                        if (params.has('type')) trojanProxy.network = params.get('type');
                        if (params.has('path')) trojanProxy['ws-path'] = params.get('path');
                        if (params.has('host')) {
                            trojanProxy['ws-headers'] = { Host: params.get('host') };
                        }

                        proxies.push(trojanProxy);
                    }
                } catch (e) { console.error('解析Trojan链接失败:', e); }
            }
            // 可以添加对其他协议的支持
        } catch (error) {
            console.error('解析代理链接错误:', error);
        }
    }

    return proxies;
}

/**
 * 识别节点区域并添加Emoji
 * @param {Object} proxy 代理节点对象
 * @param {string} sourceURL 订阅源URL
 * @returns {Object} 包含区域信息和新名称的对象
 */
function detectNodeRegion(proxy, sourceURL = '') {
    let region = '未知';
    let emoji = '❓'; // 默认Emoji

    // 用于跟踪同一区域的节点数量
    // 使用模块级别的变量来跟踪节点计数
    if (!global.regionCounts) {
        global.regionCounts = {
            '香港': 0,
            '日本': 0,
            '新加坡': 0,
            '美国': 0,
            '台湾': 0,
            '韩国': 0,
            '德国': 0,
            '英国': 0,
            '加拿大': 0,
            '澳大利亚': 0,
            '未知': 0
        };
    }

    const nameLower = proxy.name.toLowerCase();
    const serverLower = proxy.server ? proxy.server.toLowerCase() : '';

    // 优先级1: 从订阅URL判断
    if (sourceURL.includes('service=1141173')) { region = '香港'; emoji = '✨'; }
    else if (sourceURL.includes('service=910778')) { region = '日本'; emoji = '🌸'; }
    else if (sourceURL.includes('su.xfjhchr.com') || sourceURL.includes('token=657546a027866da3278413ae781daba9')) { region = '自定义'; emoji = '🌍'; }

    // 优先级2: 从节点名称判断
    if (region === '未知') {
        if (nameLower.includes('hk') || nameLower.includes('hongkong') || nameLower.includes('hong kong') || nameLower.includes('港') || nameLower.includes('✨')) { region = '香港'; emoji = '✨'; }
        else if (nameLower.includes('jp') || nameLower.includes('japan') || nameLower.includes('日本') || nameLower.includes('东京') || nameLower.includes('大阪') || nameLower.includes('🌸')) { region = '日本'; emoji = '🌸'; }
        else if (nameLower.includes('sg') || nameLower.includes('singapore') || nameLower.includes('新加坡') || nameLower.includes('🇸🇬')) { region = '新加坡'; emoji = '🇸🇬'; }
        else if (nameLower.includes('us') || nameLower.includes('united states') || nameLower.includes('美国') || nameLower.includes('🇺🇸')) { region = '美国'; emoji = '🇺🇸'; }
        else if (nameLower.includes('tw') || nameLower.includes('taiwan') || nameLower.includes('台湾') || nameLower.includes('台北') || nameLower.includes('🇹🇼')) { region = '台湾'; emoji = '🇹🇼'; }
        else if (nameLower.includes('kr') || nameLower.includes('korea') || nameLower.includes('韩国') || nameLower.includes('🇰🇷')) { region = '韩国'; emoji = '🇰🇷'; }
        else if (nameLower.includes('de') || nameLower.includes('germany') || nameLower.includes('德国') || nameLower.includes('🇩🇪')) { region = '德国'; emoji = '🇩🇪'; }
        else if (nameLower.includes('uk') || nameLower.includes('united kingdom') || nameLower.includes('英国') || nameLower.includes('🇬🇧')) { region = '英国'; emoji = '🇬🇧'; }
        else if (nameLower.includes('ca') || nameLower.includes('canada') || nameLower.includes('加拿大')) { region = '加拿大'; emoji = '🇨🇦'; }
        else if (nameLower.includes('au') || nameLower.includes('australia') || nameLower.includes('澳大利亚')) { region = '澳大利亚'; emoji = '🇦🇺'; }
    }

    // 优先级3: 从服务器地址判断
    if (region === '未知') {
        if (serverLower.includes('.hk') || serverLower.includes('hong') || serverLower.match(/^(103\.192\.|119\.236\.|202\.165\.|203\.218\.)/)) { region = '香港'; emoji = '✨'; }
        else if (serverLower.includes('.jp') || serverLower.includes('japan') || serverLower.includes('tokyo') || serverLower.match(/^(45\.76\.|139\.162\.|103\.29\.|157\.7\.)/)) { region = '日本'; emoji = '🌸'; }
        else if (serverLower.includes('.sg') || serverLower.includes('singapore') || serverLower.match(/^(128\.199\.|209\.58\.)/)) { region = '新加坡'; emoji = '🇸🇬'; }
        else if (serverLower.includes('.us') || serverLower.includes('america') || serverLower.match(/^(104\.236\.|128\.1\.|172\.104\.)/)) { region = '美国'; emoji = '🇺🇸'; }
        else if (serverLower.includes('.tw') || serverLower.includes('taiwan') || serverLower.match(/^(103\.120\.|103\.31\.)/)) { region = '台湾'; emoji = '🇹🇼'; }
        else if (serverLower.includes('.kr') || serverLower.includes('korea')) { region = '韩国'; emoji = '🇰🇷'; }
        else if (serverLower.includes('.de') || serverLower.includes('germany')) { region = '德国'; emoji = '🇩🇪'; }
        else if (serverLower.includes('.uk') || serverLower.includes('britain')) { region = '英国'; emoji = '🇬🇧'; }
        else if (serverLower.includes('.ca') || serverLower.includes('canada')) { region = '加拿大'; emoji = '🇨🇦'; }
        else if (serverLower.includes('.au') || serverLower.includes('australia')) { region = '澳大利亚'; emoji = '🇦🇺'; }
    }

    // 优先级4: 更智能的推测（基于常见模式）
    if (region === '未知') {
        // 如果服务器地址包含常见的亚洲IP段，默认为香港
        if (serverLower.match(/^(45\.78\.|66\.112\.|103\.|119\.|202\.|203\.)/)) {
            region = '香港'; emoji = '✨';
            console.log(`基于IP段推测节点 ${proxy.name} 为香港节点`);
        }
        // 如果服务器地址包含常见的美国IP段
        else if (serverLower.match(/^(104\.|128\.|172\.|192\.243\.|162\.248\.)/)) {
            region = '美国'; emoji = '🇺🇸';
            console.log(`基于IP段推测节点 ${proxy.name} 为美国节点`);
        }
        // 如果服务器地址包含常见的欧洲IP段
        else if (serverLower.match(/^(93\.179\.|185\.|188\.)/)) {
            region = '德国'; emoji = '🇩🇪';
            console.log(`基于IP段推测节点 ${proxy.name} 为德国节点`);
        }
    }

    // 增加该区域的计数
    global.regionCounts[region] = (global.regionCounts[region] || 0) + 1;

    // 根据区域生成新的名称格式
    let newName;
    if (region === '香港') {
        newName = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
    } else if (region === '日本') {
        newName = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
    } else if (region === '未知') {
        // 未知区域标记，稍后会被过滤掉
        newName = `❓❓ 未知${global.regionCounts[region]}`;
        console.log(`检测到未知节点: ${proxy.name} (服务器: ${proxy.server}) -> 将被过滤`);
    } else {
        // 其他已知区域也使用"只想睡觉"前缀
        newName = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
    }

    return { region, emoji, originalName: proxy.name, newName };
}

module.exports = {
    fetchProxiesFromSubscription,
    parseProxyLinks,
    detectNodeRegion
};