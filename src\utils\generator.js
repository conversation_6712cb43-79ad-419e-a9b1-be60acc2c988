const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { fetchProxiesFromSubscription } = require('./subscription');

/**
 * 验证代理节点配置的完整性
 * @param {Object} proxy 代理节点对象
 * @returns {boolean} 是否有效
 */
function validateProxy(proxy) {
    if (!proxy || !proxy.name || !proxy.type || !proxy.server || !proxy.port) {
        return false;
    }

    switch (proxy.type) {
        case 'ss':
        case 'ssr':
            return !!(proxy.cipher && proxy.password);
        case 'vmess':
            return !!(proxy.uuid);
        case 'trojan':
            return !!(proxy.password);
        default:
            console.warn(`未知的代理类型: ${proxy.type}`);
            return false;
    }
}

/**
 * 按国家分组排序节点
 * @param {Array} proxies 代理节点数组
 * @returns {Array} 排序后的代理节点数组
 */
function sortProxiesByRegion(proxies) {
    // 定义国家优先级顺序
    const regionOrder = {
        '香港': 1,
        '日本': 2,
        '新加坡': 3,
        '美国': 4,
        '台湾': 5,
        '韩国': 6,
        '德国': 7,
        '英国': 8,
        '加拿大': 9,
        '澳大利亚': 10,
        '未知': 999
    };

    // 从节点名称中提取国家信息
    function extractRegion(nodeName) {
        if (nodeName.includes('香港') || nodeName.includes('✨')) return '香港';
        if (nodeName.includes('日本') || nodeName.includes('🌸')) return '日本';
        if (nodeName.includes('新加坡') || nodeName.includes('🇸🇬')) return '新加坡';
        if (nodeName.includes('美国') || nodeName.includes('🇺🇸')) return '美国';
        if (nodeName.includes('台湾') || nodeName.includes('🇹🇼')) return '台湾';
        if (nodeName.includes('韩国') || nodeName.includes('🇰🇷')) return '韩国';
        if (nodeName.includes('德国') || nodeName.includes('🇩🇪')) return '德国';
        if (nodeName.includes('英国') || nodeName.includes('🇬🇧')) return '英国';
        if (nodeName.includes('加拿大') || nodeName.includes('🇨🇦')) return '加拿大';
        if (nodeName.includes('澳大利亚') || nodeName.includes('🇦🇺')) return '澳大利亚';
        return '未知';
    }

    // 从节点名称中提取数字编号
    function extractNumber(nodeName) {
        const match = nodeName.match(/(\d+)$/);
        return match ? parseInt(match[1]) : 0;
    }

    // 排序节点
    return proxies.sort((a, b) => {
        const regionA = extractRegion(a.name);
        const regionB = extractRegion(b.name);

        // 首先按国家排序
        const orderA = regionOrder[regionA] || 999;
        const orderB = regionOrder[regionB] || 999;

        if (orderA !== orderB) {
            return orderA - orderB;
        }

        // 同一国家内按数字编号排序
        const numberA = extractNumber(a.name);
        const numberB = extractNumber(b.name);

        return numberA - numberB;
    });
}

/**
 * 生成SLEEP2025.yaml配置文件
 * @param {string} header 用户自定义的头部配置
 * @param {Object} rules 用户自定义的规则
 * @param {Array} enabledSubscriptions 启用的订阅源
 * @returns {Promise<string>} 生成的配置文件路径
 */
async function generateConfig(header, rules, enabledSubscriptions) {
    console.log('开始生成SLEEP2025.yaml配置文件');

    // 重置节点计数器，确保每次生成配置文件时节点编号从1开始
    global.regionCounts = {
        '香港': 0,
        '日本': 0,
        '新加坡': 0,
        '美国': 0,
        '台湾': 0,
        '韩国': 0,
        '德国': 0,
        '英国': 0,
        '未知': 0
    };

    // 重置区域计数器
    global.regionCounts = {
        '香港': 0,
        '日本': 0,
        '新加坡': 0,
        '美国': 0,
        '台湾': 0,
        '韩国': 0,
        '德国': 0,
        '英国': 0,
        '加拿大': 0,
        '澳大利亚': 0,
        '未知': 0
    };

    // 获取所有订阅节点
    const allProxies = [];
    for (const subscription of enabledSubscriptions) {
        try {
            const proxies = await fetchProxiesFromSubscription(subscription);
            // 验证和过滤节点
            const validProxies = proxies.filter(proxy => {
                const isValid = validateProxy(proxy);
                if (!isValid) {
                    console.warn(`跳过无效节点: ${proxy.name || '未知'} (类型: ${proxy.type || '未知'})`);
                }
                return isValid;
            });

            allProxies.push(...validProxies);
            console.log(`从订阅 ${subscription} 获取了 ${proxies.length} 个节点，其中 ${validProxies.length} 个有效`);
        } catch (error) {
            console.error(`获取订阅 ${subscription} 失败:`, error.message);
            // 继续处理下一个订阅
        }
    }

    console.log(`总共获取了 ${allProxies.length} 个有效节点`);

    if (allProxies.length === 0) {
        throw new Error('没有获取到任何有效的代理节点');
    }

    // 过滤掉未知节点
    const knownProxies = allProxies.filter(proxy => {
        const isUnknown = proxy.name.includes('❓') || proxy.name.includes('未知');
        if (isUnknown) {
            console.log(`跳过未知节点: ${proxy.name}`);
        }
        return !isUnknown;
    });

    console.log(`过滤后剩余 ${knownProxies.length} 个已知国家节点`);

    // 按国家分组排序节点
    const sortedProxies = sortProxiesByRegion(knownProxies);
    console.log(`节点已按国家分组排序`);

    // 生成北京时间
    const now = new Date();
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const formattedTime = beijingTime.toISOString().replace('T', ' ').substring(0, 19);

    // 生成节点注释
    let nodeListComment = "# Proxies:\n";
    sortedProxies.forEach(proxy => {
        nodeListComment += `# - ${proxy.name}\n`;
    });

    // 构建最终配置
    let finalConfig = `# File Generated: ${formattedTime} (Beijing Time)\n`;
    finalConfig += `# SLEEP2025 - Made with Love\n\n`;

    // 1. 添加头部配置（如果有）
    if (header && header.trim()) {
        try {
            // 验证和格式化YAML
            const headerObj = yaml.load(header);
            finalConfig += yaml.dump(headerObj, { lineWidth: -1 });
        } catch (error) {
            console.error('头部YAML格式错误，使用原始内容:', error.message);
            finalConfig += header.trim() + '\n\n';
        }
    }

    // 2. 添加代理节点列表
    finalConfig += `proxies:\n`;
    sortedProxies.forEach(proxy => {
        try {
            // 安全地转义节点名称
            const safeName = proxy.name.replace(/"/g, '\\"');
            finalConfig += `  - name: "${safeName}"\n`;
            finalConfig += `    type: ${proxy.type}\n`;
            finalConfig += `    server: ${proxy.server}\n`;
            finalConfig += `    port: ${proxy.port}\n`;

            if (proxy.type === 'ss' || proxy.type === 'ssr') {
                finalConfig += `    cipher: ${proxy.cipher || 'aes-256-gcm'}\n`;
                finalConfig += `    password: "${proxy.password.replace(/"/g, '\\"')}"\n`;

                if (proxy.plugin) {
                    finalConfig += `    plugin: ${proxy.plugin}\n`;
                    if (proxy['plugin-opts']) {
                        finalConfig += `    plugin-opts:\n`;
                        for (const key in proxy['plugin-opts']) {
                            const value = proxy['plugin-opts'][key];
                            if (typeof value === 'string') {
                                finalConfig += `      ${key}: "${value.replace(/"/g, '\\"')}"\n`;
                            } else {
                                finalConfig += `      ${key}: ${value}\n`;
                            }
                        }
                    }
                }
            } else if (proxy.type === 'vmess') {
                finalConfig += `    uuid: ${proxy.uuid}\n`;
                finalConfig += `    alterId: ${proxy.alterId || 0}\n`;
                finalConfig += `    cipher: ${proxy.cipher || 'auto'}\n`;

                if (proxy.tls !== undefined) finalConfig += `    tls: ${proxy.tls}\n`;
                if (proxy.network) finalConfig += `    network: ${proxy.network}\n`;

                // 处理ws-path和ws-headers
                if (proxy['ws-path']) finalConfig += `    ws-path: "${proxy['ws-path'].replace(/"/g, '\\"')}"\n`;
                if (proxy['ws-headers']) {
                    finalConfig += `    ws-headers:\n`;
                    for (const key in proxy['ws-headers']) {
                        finalConfig += `      ${key}: "${proxy['ws-headers'][key].replace(/"/g, '\\"')}"\n`;
                    }
                }

                if (proxy.sni) finalConfig += `    sni: ${proxy.sni}\n`;
            } else if (proxy.type === 'trojan') {
                const password = proxy.password || 'default-password';
                finalConfig += `    password: "${password.replace(/"/g, '\\"')}"\n`;

                // trojan默认配置
                finalConfig += `    tls: ${proxy.tls !== undefined ? proxy.tls : true}\n`;
                finalConfig += `    skip-cert-verify: ${proxy['skip-cert-verify'] !== undefined ? proxy['skip-cert-verify'] : false}\n`;

                if (proxy.sni) finalConfig += `    sni: ${proxy.sni}\n`;
                if (proxy.network) finalConfig += `    network: ${proxy.network}\n`;

                // 处理ws相关配置
                if (proxy['ws-path']) finalConfig += `    ws-path: "${proxy['ws-path'].replace(/"/g, '\\"')}"\n`;
                if (proxy['ws-headers']) {
                    finalConfig += `    ws-headers:\n`;
                    for (const key in proxy['ws-headers']) {
                        finalConfig += `      ${key}: "${proxy['ws-headers'][key].replace(/"/g, '\\"')}"\n`;
                    }
                }
            }

            finalConfig += '\n';
        } catch (error) {
            console.error(`生成节点配置失败: ${proxy.name}`, error);
            // 跳过有问题的节点，继续处理下一个
        }
    });

    // 3. 检查头部中是否已有proxy-groups
    let hasProxyGroups = false;
    if (header) {
        try {
            const headerObj = yaml.load(header);
            hasProxyGroups = headerObj && headerObj['proxy-groups'] && Array.isArray(headerObj['proxy-groups']);
        } catch (e) {
            // 如果YAML解析失败，假设没有proxy-groups
            hasProxyGroups = false;
        }
    }

    // 如果头部没有定义proxy-groups，添加固定的"只想睡觉"分组
    if (!hasProxyGroups) {
        finalConfig += `proxy-groups:\n`;
        finalConfig += `  - name: 只想睡觉\n`;
        finalConfig += `    type: select\n`;
        finalConfig += `    proxies:\n`;
        finalConfig += `      - DIRECT\n`;

        // 添加所有节点到"只想睡觉"组
        sortedProxies.forEach(proxy => {
            finalConfig += `      - "${proxy.name}"\n`;
        });

        finalConfig += '\n';
    }

    // 4. 添加规则
    finalConfig += `rules:\n`;

    // 添加用户自定义规则
    if (rules && rules.userCustomRules) {
        const userRules = rules.userCustomRules.trim().split('\n');
        userRules.forEach(rule => {
            rule = rule.trim();
            if (rule && !rule.startsWith('#')) {
                // 检查规则是否已经包含破折号前缀
                if (rule.startsWith("- '") || rule.startsWith('-  ')) {
                    // 如果已经有破折号，去掉它并重新格式化
                    const cleanRule = rule.replace(/^-\s*/, '');
                    finalConfig += `  - ${cleanRule}\n`;
                } else {
                    finalConfig += `  - ${rule}\n`;
                }
            }
        });
    }

    // 添加已有规则
    if (rules && rules.existingRules) {
        const existingRules = rules.existingRules.trim().split('\n');
        existingRules.forEach(rule => {
            rule = rule.trim();
            if (rule && !rule.startsWith('#')) {
                // 检查规则是否已经包含破折号前缀
                if (rule.startsWith("- '") || rule.startsWith('-  ')) {
                    // 如果已经有破折号，去掉它并重新格式化
                    const cleanRule = rule.replace(/^-\s*/, '');
                    finalConfig += `  - ${cleanRule}\n`;
                } else {
                    finalConfig += `  - ${rule}\n`;
                }
            }
        });
    }

    // 检查用户规则中是否已经包含MATCH规则
    const hasMatchRule = (rules.userCustomRules && rules.userCustomRules.includes('MATCH')) ||
                        (rules.existingRules && rules.existingRules.includes('MATCH'));

    // 如果用户规则中没有MATCH规则，则添加默认的MATCH规则
    if (!hasMatchRule) {
        finalConfig += `  - MATCH,只想睡觉\n`;
        console.log('添加默认MATCH规则');
    } else {
        console.log('用户规则中已包含MATCH规则，跳过添加默认规则');
    }

    // 保存配置文件
    const configDir = path.join(__dirname, '../../configs');
    if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
    }

    const configFileName = 'SLEEP2025';
    const configFilePath = path.join(configDir, `${configFileName}.yaml`);
    fs.writeFileSync(configFilePath, finalConfig);

    console.log(`配置文件已生成: ${configFilePath}`);
    return configFileName;
}

module.exports = {
    generateConfig
};