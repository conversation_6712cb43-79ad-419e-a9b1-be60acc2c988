<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clash配置生成与合并工具 - SLEEP2025</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- 引入Bootstrap CSS -->
    <link href="libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Bootstrap图标 -->
    <link rel="stylesheet" href="libs/bootstrap-icons/bootstrap-icons.css">

    <!-- 引入CodeMirror库 -->
    <link rel="stylesheet" href="libs/codemirror/css/codemirror.min.css">
    <link rel="stylesheet" href="libs/codemirror/css/monokai.min.css">
    <link rel="stylesheet" href="libs/codemirror/css/simplescrollbars.min.css">
    <script src="libs/codemirror/js/codemirror.min.js"></script>
    <script src="libs/codemirror/js/yaml.min.js"></script>
    <script src="libs/codemirror/js/active-line.min.js"></script>
    <script src="libs/codemirror/js/simplescrollbars.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="my-4 text-center">
            <h1>Clash配置生成与合并工具</h1>
            <p class="lead">SLEEP2025 - Made with Love</p>
        </header>

        <div class="card mb-4">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="nodes-tab" data-bs-toggle="tab" data-bs-target="#nodes" type="button" role="tab" aria-controls="nodes" aria-selected="true">节点管理</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="header-tab" data-bs-toggle="tab" data-bs-target="#header" type="button" role="tab" aria-controls="header" aria-selected="false">头部编辑</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="rules-tab" data-bs-toggle="tab" data-bs-target="#rules" type="button" role="tab" aria-controls="rules" aria-selected="false">规则编辑</button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="myTabContent">
                    <!-- 节点管理Tab -->
                    <div class="tab-pane fade show active" id="nodes" role="tabpanel" aria-labelledby="nodes-tab">
                        <h4 class="card-title">订阅节点管理</h4>
                        <p class="card-text">添加Clash节点订阅链接，并启用需要的订阅源。</p>

                        <div id="subscription-list">
                            <div class="subscription-row mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control subscription-url" placeholder="Clash订阅链接">
                                    <div class="input-group-text">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input subscription-enabled" type="checkbox" checked>
                                            <label class="form-check-label">启用</label>
                                        </div>
                                    </div>
                                    <button class="btn btn-outline-danger remove-subscription" type="button">删除</button>
                                </div>
                            </div>
                            <div class="subscription-row mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control subscription-url" placeholder="Clash订阅链接">
                                    <div class="input-group-text">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input subscription-enabled" type="checkbox" checked>
                                            <label class="form-check-label">启用</label>
                                        </div>
                                    </div>
                                    <button class="btn btn-outline-danger remove-subscription" type="button">删除</button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button id="add-subscription" class="btn btn-outline-primary me-2">
                                <i class="bi bi-plus-circle"></i> 添加订阅
                            </button>
                            <button id="fetch-nodes-btn" class="btn btn-primary">
                                <i class="bi bi-cloud-download"></i> 获取节点
                            </button>
                        </div>

                        <div id="nodes-result" class="mt-3 d-none">
                            <div class="alert alert-info">
                                <p><strong>节点获取结果:</strong></p>
                                <div id="nodes-result-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 头部编辑Tab -->
                    <div class="tab-pane fade" id="header" role="tabpanel" aria-labelledby="header-tab">
                        <h4 class="card-title">Clash配置头部编辑</h4>
                        <p class="card-text">编辑Clash配置文件的头部信息（端口、DNS、自定义代理组等）。</p>

                        <div class="form-group">
                            <div id="header-editor-container" class="code-editor-container">
                                <textarea id="header-content" class="form-control code-editor" placeholder="# 示例头部配置
port: 7890
socks-port: 7891
allow-lan: true
mode: rule
log-level: info
external-controller: '0.0.0.0:9090'
dns:
  enable: true
  listen: 0.0.0.0:53
  enhanced-mode: redir-host
  nameserver:
    - *********
    - ************"></textarea>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button id="save-header-btn" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存头部配置
                            </button>
                        </div>
                    </div>

                    <!-- 规则编辑Tab -->
                    <div class="tab-pane fade" id="rules" role="tabpanel" aria-labelledby="rules-tab">
                        <h4 class="card-title">Clash规则编辑</h4>
                        <p class="card-text">编辑自定义规则和已有规则。</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="user-rules">用户自定义规则</label>
                                    <div id="user-rules-editor-container" class="code-editor-container">
                                        <textarea id="user-rules" class="form-control code-editor" placeholder="# 用户自定义规则，一行一条
DOMAIN-SUFFIX,example.com,只想睡觉
IP-CIDR,***********/24,DIRECT"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="existing-rules">已有规则</label>
                                    <div id="existing-rules-editor-container" class="code-editor-container">
                                        <textarea id="existing-rules" class="form-control code-editor" placeholder="# 已有的基本规则，一行一条
DOMAIN-SUFFIX,google.com,只想睡觉
DOMAIN-KEYWORD,youtube,只想睡觉
GEOIP,CN,DIRECT"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button id="save-rules-btn" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存规则配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="d-flex gap-2 flex-wrap">
                            <button id="generate-config-btn" class="btn btn-success">
                                <i class="bi bi-gear"></i> 生成配置
                            </button>
                            <button id="toggle-auto-task-btn" class="btn btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#auto-task-config">
                                <i class="bi bi-clock"></i> 定时任务
                            </button>
                        </div>
                    </div>

                    <!-- 定时任务配置 -->
                    <div class="col-12 mb-3">
                        <div class="collapse" id="auto-task-config">
                            <div class="card card-body bg-light">
                                <h6 class="card-title">
                                    <i class="bi bi-clock-history"></i> 自动生成配置
                                </h6>
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="auto-task-enabled">
                                            <label class="form-check-label" for="auto-task-enabled">
                                                启用定时自动生成配置
                                            </label>
                                        </div>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">每天</span>
                                            <input type="time" id="auto-task-time" class="form-control" value="08:00">
                                            <span class="input-group-text">自动执行</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="text-muted small">
                                            <div><strong>状态：</strong><span id="auto-task-status">未启用</span></div>
                                            <div><strong>下次执行：</strong><span id="next-execution-time">-</span></div>
                                            <div><strong>上次执行：</strong><span id="last-execution-time">-</span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <button id="save-auto-task-btn" class="btn btn-sm btn-primary">
                                        <i class="bi bi-save"></i> 保存设置
                                    </button>
                                    <button id="run-task-now-btn" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-play-fill"></i> 立即执行
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12">
                        <div id="config-url-container">
                            <div class="input-group">
                                <span class="input-group-text">配置链接</span>
                                <input type="text" id="config-url" class="form-control" readonly>
                                <button id="copy-config-url-btn" class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-clipboard"></i> 复制
                                </button>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-1">
                                <small class="text-muted">可直接添加此链接到Clash客户端</small>
                                <small class="text-muted" id="config-generation-time">-</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态和日志 -->
        <div id="status-container" class="d-none">
            <div class="alert" role="alert">
                <span id="status-message"></span>
            </div>
        </div>

        <footer class="text-center py-3 text-muted">
            <p>&copy; 2023-2025 SLEEP2025. Made with ❤️</p>
            <div class="mt-2">
                <button id="check-filesystem-btn" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-hdd"></i> 检查文件系统
                </button>
            </div>
            <div id="filesystem-result" class="mt-2 d-none">
                <div class="alert alert-info">
                    <p><strong>文件系统检查结果:</strong></p>
                    <div id="filesystem-result-content" class="small text-start"></div>
                </div>
            </div>
        </footer>
    </div>

    <!-- 加载中遮罩 -->
    <div id="loading-overlay" class="d-none">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
</body>
</html>