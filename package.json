{"name": "clash_config_tool", "version": "1.0.0", "description": "Clash配置生成与合并工具 - SLEEP2025", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "download-deps": "node download-assets.js", "setup": "npm run download-deps && echo 依赖下载完成，现在可以运行 npm start 启动服务器", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["clash", "proxy", "config", "yaml"], "author": "SLEEP2025", "license": "MIT", "dependencies": {"axios": "^1.6.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "js-yaml": "^4.1.0", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1"}}